//@version=5
strategy("VWAP Reversion and Trend Confirmation", shorttitle="VWAP Reversion", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// VWAP Settings
vwap_source = input.source(hlc3, title="VWAP Source", group="VWAP Settings")
vwap_reset = input.string("Session", title="VWAP Reset", options=["Session", "Daily", "Weekly", "Monthly"], group="VWAP Settings")

// Trend Confirmation Settings
trend_length = input.int(20, title="Trend EMA Length", minval=5, maxval=100, group="Trend Settings")
higher_tf = input.timeframe("15", title="Higher Timeframe for Trend", group="Trend Settings")

// Entry Settings
pullback_threshold = input.float(0.02, title="VWAP Pullback Threshold (%)", minval=0.01, maxval=1.0, step=0.01, group="Entry Settings")
confirmation_bars = input.int(2, title="Confirmation Bars", minval=1, maxval=5, group="Entry Settings")

// Risk Management
stop_loss_atr_mult = input.float(2.0, title="Stop Loss ATR Multiplier", minval=0.5, maxval=5.0, step=0.1, group="Risk Management")
take_profit_rr = input.float(2.0, title="Take Profit Risk/Reward Ratio", minval=1.0, maxval=5.0, step=0.1, group="Risk Management")
atr_length = input.int(14, title="ATR Length", minval=5, maxval=50, group="Risk Management")

// Time Filter
use_time_filter = input.bool(true, title="Use Time Filter", group="Time Filter")
start_hour = input.int(9, title="Start Hour (EST)", minval=0, maxval=23, group="Time Filter")
end_hour = input.int(16, title="End Hour (EST)", minval=0, maxval=23, group="Time Filter")

// ============================================================================
// CALCULATIONS
// ============================================================================

// VWAP Calculation
vwap_value = switch vwap_reset
    "Session" => ta.vwap(vwap_source)
    "Daily" => ta.vwap(vwap_source, timeframe.change("1D"))
    "Weekly" => ta.vwap(vwap_source, timeframe.change("1W"))
    "Monthly" => ta.vwap(vwap_source, timeframe.change("1M"))
    => ta.vwap(vwap_source)

// Trend Analysis
trend_ema = ta.ema(close, trend_length)
higher_tf_trend = request.security(syminfo.tickerid, higher_tf, ta.ema(close, trend_length))

// ATR for volatility-based stops
atr_value = ta.atr(atr_length)

// Time Filter
est_offset = -5 // EST is UTC-5
current_hour = hour(time, "UTC" + str.tostring(est_offset))
time_allowed = not use_time_filter or (current_hour >= start_hour and current_hour <= end_hour)

// ============================================================================
// TREND IDENTIFICATION
// ============================================================================

// Primary trend conditions
bullish_trend = close > trend_ema and trend_ema > trend_ema[5] and close > higher_tf_trend
bearish_trend = close < trend_ema and trend_ema < trend_ema[5] and close < higher_tf_trend

// VWAP trend alignment
price_above_vwap = close > vwap_value
price_below_vwap = close < vwap_value

// Strong trend confirmation
strong_bullish_trend = bullish_trend and price_above_vwap and close > close[5]
strong_bearish_trend = bearish_trend and price_below_vwap and close < close[5]

// ============================================================================
// ENTRY CONDITIONS
// ============================================================================

// VWAP pullback detection
vwap_distance = math.abs(close - vwap_value) / close * 100
near_vwap = vwap_distance <= pullback_threshold

// Bullish setup conditions
bullish_pullback = strong_bullish_trend[1] and low <= vwap_value and close > vwap_value
bullish_bounce_confirmation = close > high[1] and close > vwap_value

// Bearish setup conditions  
bearish_pullback = strong_bearish_trend[1] and high >= vwap_value and close < vwap_value
bearish_rejection_confirmation = close < low[1] and close < vwap_value

// Confirmation logic
var int bullish_confirm_count = 0
var int bearish_confirm_count = 0

if bullish_pullback
    bullish_confirm_count := 1
else if bullish_confirm_count > 0 and bullish_confirm_count < confirmation_bars and bullish_bounce_confirmation
    bullish_confirm_count += 1
else if not bullish_bounce_confirmation
    bullish_confirm_count := 0

if bearish_pullback
    bearish_confirm_count := 1
else if bearish_confirm_count > 0 and bearish_confirm_count < confirmation_bars and bearish_rejection_confirmation
    bearish_confirm_count += 1
else if not bearish_rejection_confirmation
    bearish_confirm_count := 0

// Final entry signals
long_signal = bullish_confirm_count >= confirmation_bars and time_allowed and strategy.position_size == 0
short_signal = bearish_confirm_count >= confirmation_bars and time_allowed and strategy.position_size == 0

// ============================================================================
// POSITION MANAGEMENT
// ============================================================================

// Calculate stop loss and take profit levels
long_stop = close - (atr_value * stop_loss_atr_mult)
short_stop = close + (atr_value * stop_loss_atr_mult)

long_tp = close + (math.abs(close - long_stop) * take_profit_rr)
short_tp = close - (math.abs(short_stop - close) * take_profit_rr)

// Entry orders
if long_signal
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=long_stop, limit=long_tp)
    bullish_confirm_count := 0

if short_signal
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=short_stop, limit=short_tp)
    bearish_confirm_count := 0

// ============================================================================
// VISUALIZATION
// ============================================================================

// Plot VWAP
plot(vwap_value, color=color.blue, linewidth=2, title="VWAP")

// Plot trend EMA
plot(trend_ema, color=bullish_trend ? color.green : bearish_trend ? color.red : color.gray, linewidth=1, title="Trend EMA")

// VWAP bands for pullback visualization
vwap_upper = vwap_value * (1 + pullback_threshold / 100)
vwap_lower = vwap_value * (1 - pullback_threshold / 100)
plot(vwap_upper, color=color.blue, linewidth=1, style=plot.style_circles, title="VWAP Upper Band")
plot(vwap_lower, color=color.blue, linewidth=1, style=plot.style_circles, title="VWAP Lower Band")

// Background color for trend
bgcolor(strong_bullish_trend ? color.new(color.green, 95) : strong_bearish_trend ? color.new(color.red, 95) : na)

// Entry signals
plotshape(long_signal, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.normal, title="Long Signal")
plotshape(short_signal, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal, title="Short Signal")

// ============================================================================
// ALERTS
// ============================================================================

alertcondition(long_signal, title="VWAP Long Signal", message="VWAP Reversion Long Entry Signal")
alertcondition(short_signal, title="VWAP Short Signal", message="VWAP Reversion Short Entry Signal")

// ============================================================================
// STRATEGY INFORMATION
// ============================================================================

// Display current strategy state
var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Trend", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, strong_bullish_trend ? "Bullish" : strong_bearish_trend ? "Bearish" : "Neutral", 
               text_color=color.white, bgcolor=strong_bullish_trend ? color.green : strong_bearish_trend ? color.red : color.gray)
    
    table.cell(info_table, 0, 1, "VWAP Dist", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, str.tostring(vwap_distance, "#.##") + "%", text_color=color.black, bgcolor=color.white)
    
    table.cell(info_table, 0, 2, "Position", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "Flat", 
               text_color=color.white, bgcolor=strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray)
