//@version=5
strategy("Enhanced VWAP Reversion Pro", shorttitle="VWAP Pro", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10, max_bars_back=500)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// VWAP Settings
vwap_source = input.source(hlc3, title="VWAP Source", group="VWAP Settings")
vwap_reset = input.string("Session", title="VWAP Reset", options=["Session", "Daily", "Weekly", "Monthly"], group="VWAP Settings")
use_vwap_bands = input.bool(true, title="Use VWAP Standard Deviation Bands", group="VWAP Settings")
vwap_std_mult = input.float(1.0, title="VWAP Std Dev Multiplier", minval=0.5, maxval=3.0, step=0.1, group="VWAP Settings")

// Trend Confirmation Settings
trend_length = input.int(20, title="Trend EMA Length", minval=5, maxval=100, group="Trend Settings")
higher_tf = input.timeframe("15", title="Higher Timeframe for Trend", group="Trend Settings")
use_multi_tf = input.bool(true, title="Use Multi-Timeframe Analysis", group="Trend Settings")
trend_strength_threshold = input.float(0.5, title="Trend Strength Threshold", minval=0.1, maxval=2.0, step=0.1, group="Trend Settings")

// Entry Settings
pullback_threshold = input.float(0.05, title="VWAP Pullback Threshold (%)", minval=0.01, maxval=1.0, step=0.01, group="Entry Settings")
confirmation_bars = input.int(1, title="Confirmation Bars", minval=1, maxval=5, group="Entry Settings")
use_volume_filter = input.bool(false, title="Use Volume Confirmation", group="Entry Settings")
volume_threshold = input.float(1.2, title="Volume Spike Threshold", minval=1.0, maxval=5.0, step=0.1, group="Entry Settings")
use_momentum_filter = input.bool(false, title="Use Momentum Filter", group="Entry Settings")
quality_threshold_input = input.float(2.0, title="Quality Threshold", minval=1.0, maxval=6.5, step=0.5, group="Entry Settings")
use_quality_filter = input.bool(true, title="Enable Quality Filter", group="Entry Settings")

// Advanced Risk Management
stop_loss_atr_mult = input.float(2.0, title="Stop Loss ATR Multiplier", minval=0.5, maxval=5.0, step=0.1, group="Risk Management")
take_profit_rr = input.float(2.0, title="Take Profit Risk/Reward Ratio", minval=1.0, maxval=5.0, step=0.1, group="Risk Management")
atr_length = input.int(14, title="ATR Length", minval=5, maxval=50, group="Risk Management")
use_trailing_stop = input.bool(true, title="Use Trailing Stop", group="Risk Management")
trailing_atr_mult = input.float(1.5, title="Trailing Stop ATR Multiplier", minval=0.5, maxval=3.0, step=0.1, group="Risk Management")
use_partial_tp = input.bool(true, title="Use Partial Profit Taking", group="Risk Management")
partial_tp_percent = input.float(50.0, title="Partial TP Percentage", minval=25.0, maxval=75.0, step=5.0, group="Risk Management")

// Market Context Filters
use_volatility_filter = input.bool(false, title="Use Volatility Filter", group="Market Context")
volatility_threshold = input.float(2.0, title="Volatility Threshold", minval=0.5, maxval=3.0, step=0.1, group="Market Context")
use_market_regime = input.bool(false, title="Use Market Regime Detection", group="Market Context")
regime_lookback = input.int(50, title="Market Regime Lookback", minval=20, maxval=200, group="Market Context")

// Time Filter
use_time_filter = input.bool(true, title="Use Time Filter", group="Time Filter")
start_hour = input.int(9, title="Start Hour (EST)", minval=0, maxval=23, group="Time Filter")
end_hour = input.int(16, title="End Hour (EST)", minval=0, maxval=23, group="Time Filter")
avoid_news_times = input.bool(true, title="Avoid Major News Times", group="Time Filter")

// ============================================================================
// CALCULATIONS
// ============================================================================

// VWAP Calculation with Standard Deviation Bands
vwap_value = switch vwap_reset
    "Session" => ta.vwap(vwap_source)
    "Daily" => ta.vwap(vwap_source, timeframe.change("1D"))
    "Weekly" => ta.vwap(vwap_source, timeframe.change("1W"))
    "Monthly" => ta.vwap(vwap_source, timeframe.change("1M"))
    => ta.vwap(vwap_source)

// VWAP Standard Deviation Bands
vwap_stdev = ta.stdev(vwap_source, 20)
vwap_upper_band = vwap_value + (vwap_stdev * vwap_std_mult)
vwap_lower_band = vwap_value - (vwap_stdev * vwap_std_mult)

// Enhanced Trend Analysis
trend_ema = ta.ema(close, trend_length)
trend_ema_fast = ta.ema(close, trend_length / 2)
trend_ema_slow = ta.ema(close, trend_length * 2)

// Multi-timeframe trend analysis
higher_tf_trend = request.security(syminfo.tickerid, higher_tf, ta.ema(close, trend_length))
higher_tf_close = request.security(syminfo.tickerid, higher_tf, close)
higher_tf_vwap = request.security(syminfo.tickerid, higher_tf, ta.vwap(vwap_source))

// Trend strength calculation
trend_strength = (close - trend_ema_slow) / trend_ema_slow * 100
trend_momentum = ta.roc(trend_ema, 5)

// Volume Analysis
volume_ma = ta.sma(volume, 20)
volume_spike = volume > (volume_ma * volume_threshold)
relative_volume = volume / volume_ma

// Momentum Indicators
rsi = ta.rsi(close, 14)
macd_line = ta.ema(close, 12) - ta.ema(close, 26)
macd_signal = ta.ema(macd_line, 9)
macd_histogram = macd_line - macd_signal

// Market Regime Detection
price_range = ta.atr(regime_lookback)
trend_consistency = ta.correlation(close, bar_index, regime_lookback)
market_regime = math.abs(trend_consistency) > 0.3 ? "Trending" : "Ranging"

// Volatility Analysis
current_volatility = ta.atr(atr_length)
volatility_ma = ta.sma(current_volatility, 20)
volatility_ratio = current_volatility / volatility_ma
high_volatility = volatility_ratio > volatility_threshold

// ATR for volatility-based stops
atr_value = ta.atr(atr_length)

// Time Filter with News Avoidance
est_offset = -5 // EST is UTC-5
current_hour = hour(time, "UTC" + str.tostring(est_offset))
current_minute = minute(time)

// Major news times to avoid (EST): 8:30 AM, 10:00 AM, 2:00 PM
news_time_830 = current_hour == 8 and current_minute >= 25 and current_minute <= 35
news_time_1000 = current_hour == 10 and current_minute >= 0 and current_minute <= 10
news_time_1400 = current_hour == 14 and current_minute >= 0 and current_minute <= 10
is_news_time = avoid_news_times and (news_time_830 or news_time_1000 or news_time_1400)

time_allowed = not use_time_filter or (current_hour >= start_hour and current_hour <= end_hour and not is_news_time)

// ============================================================================
// ENHANCED TREND IDENTIFICATION
// ============================================================================

// Primary trend conditions with multiple confirmations
basic_bullish_trend = close > trend_ema and trend_ema > trend_ema[5]
basic_bearish_trend = close < trend_ema and trend_ema < trend_ema[5]

// Multi-timeframe trend alignment
htf_bullish = close > higher_tf_trend and higher_tf_close > higher_tf_vwap
htf_bearish = close < higher_tf_trend and higher_tf_close < higher_tf_vwap

// Enhanced trend conditions (simplified)
bullish_trend = basic_bullish_trend and (not use_multi_tf or htf_bullish)
bearish_trend = basic_bearish_trend and (not use_multi_tf or htf_bearish)

// Trend strength validation (more lenient)
strong_trend = not use_market_regime or math.abs(trend_strength) > trend_strength_threshold
trending_market = not use_market_regime or market_regime == "Trending"

// VWAP trend alignment with bands
price_above_vwap = close > vwap_value
price_below_vwap = close < vwap_value
price_in_upper_band = close > vwap_upper_band
price_in_lower_band = close < vwap_lower_band

// Momentum confirmation (more flexible)
bullish_momentum = not use_momentum_filter or (trend_momentum > 0 and rsi > 45)
bearish_momentum = not use_momentum_filter or (trend_momentum < 0 and rsi < 55)

// Volume confirmation for trend (more lenient)
volume_confirms_trend = not use_volume_filter or relative_volume > 1.0

// Strong trend confirmation with flexible filters
strong_bullish_trend = bullish_trend and (price_above_vwap or close > close[3]) and strong_trend and trending_market and
                      bullish_momentum and volume_confirms_trend and
                      (not use_volatility_filter or not high_volatility)

strong_bearish_trend = bearish_trend and (price_below_vwap or close < close[3]) and strong_trend and trending_market and
                      bearish_momentum and volume_confirms_trend and
                      (not use_volatility_filter or not high_volatility)

// ============================================================================
// ENHANCED ENTRY CONDITIONS
// ============================================================================

// VWAP pullback detection with bands
vwap_distance = math.abs(close - vwap_value) / close * 100
near_vwap = vwap_distance <= pullback_threshold

// Enhanced pullback detection using VWAP bands
bullish_pullback_zone = use_vwap_bands ? (low <= vwap_value and low >= vwap_lower_band) : (low <= vwap_value)
bearish_pullback_zone = use_vwap_bands ? (high >= vwap_value and high <= vwap_upper_band) : (high >= vwap_value)

// Volume confirmation for entries
entry_volume_spike = not use_volume_filter or volume_spike
volume_quality = relative_volume >= 1.0

// Smart money detection (institutional activity indicators)
large_candle = (high - low) > (atr_value * 0.8)
strong_close = math.abs(close - open) / (high - low) > 0.6
institutional_activity = large_candle and strong_close and entry_volume_spike

// Enhanced bullish setup conditions - FIXED to avoid look-ahead bias
bullish_pullback = strong_bullish_trend and bullish_pullback_zone
bullish_bounce_confirmation = close > open and close > vwap_value and (not use_volume_filter or entry_volume_spike) and (rsi > 45 or not use_momentum_filter)

// Enhanced bearish setup conditions - FIXED to avoid look-ahead bias
bearish_pullback = strong_bearish_trend and bearish_pullback_zone
bearish_rejection_confirmation = close < open and close < vwap_value and (not use_volume_filter or entry_volume_spike) and (rsi < 55 or not use_momentum_filter)

// Advanced confirmation logic with quality filters
var int bullish_confirm_count = 0
var int bearish_confirm_count = 0
var float entry_quality_score = 0.0

// Calculate entry quality score
calculate_quality_score(is_bullish) =>
    score = 0.0
    score += strong_trend ? 1.0 : 0.0
    score += trending_market ? 1.0 : 0.0
    score += entry_volume_spike ? 1.0 : 0.0
    score += institutional_activity ? 1.0 : 0.0
    score += (is_bullish ? bullish_momentum : bearish_momentum) ? 1.0 : 0.0
    score += volume_quality ? 0.5 : 0.0
    score

if bullish_pullback
    bullish_confirm_count := 1
    entry_quality_score := calculate_quality_score(true)
else if bullish_confirm_count > 0 and bullish_confirm_count < confirmation_bars and bullish_bounce_confirmation
    bullish_confirm_count += 1
else if not bullish_bounce_confirmation
    bullish_confirm_count := 0
    entry_quality_score := 0.0

if bearish_pullback
    bearish_confirm_count := 1
    entry_quality_score := calculate_quality_score(false)
else if bearish_confirm_count > 0 and bearish_confirm_count < confirmation_bars and bearish_rejection_confirmation
    bearish_confirm_count += 1
else if not bearish_rejection_confirmation
    bearish_confirm_count := 0
    entry_quality_score := 0.0

// Quality threshold for entries (configurable)
high_quality_setup = not use_quality_filter or entry_quality_score >= quality_threshold_input

// Final entry signals with quality filter
long_signal = bullish_confirm_count >= confirmation_bars and time_allowed and
              strategy.position_size == 0 and high_quality_setup

short_signal = bearish_confirm_count >= confirmation_bars and time_allowed and
               strategy.position_size == 0 and high_quality_setup

// ============================================================================
// ADVANCED POSITION MANAGEMENT
// ============================================================================

// Dynamic position sizing based on volatility and quality
base_position_size = 10.0 // Base percentage of equity
volatility_adjustment = high_volatility ? 0.7 : 1.0 // Reduce size in high volatility
quality_adjustment = entry_quality_score / 6.5 // Scale based on setup quality
dynamic_position_size = base_position_size * volatility_adjustment * quality_adjustment

// Calculate stop loss levels with market structure consideration
swing_low = ta.lowest(low, 10)
swing_high = ta.highest(high, 10)

long_stop_atr = close - (atr_value * stop_loss_atr_mult)
long_stop_structure = swing_low - (atr_value * 0.5)
long_stop = math.min(long_stop_atr, long_stop_structure)

short_stop_atr = close + (atr_value * stop_loss_atr_mult)
short_stop_structure = swing_high + (atr_value * 0.5)
short_stop = math.max(short_stop_atr, short_stop_structure)

// Calculate take profit levels
long_tp_full = close + (math.abs(close - long_stop) * take_profit_rr)
short_tp_full = close - (math.abs(short_stop - close) * take_profit_rr)

// Partial profit taking levels
long_tp_partial = close + (math.abs(close - long_stop) * (take_profit_rr * 0.6))
short_tp_partial = close - (math.abs(short_stop - close) * (take_profit_rr * 0.6))

// Trailing stop variables
var float long_trailing_stop = na
var float short_trailing_stop = na
var bool partial_tp_taken = false

// Entry orders with FIXED position sizing
if long_signal
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=long_stop, limit=long_tp_full)

    // Initialize trailing stop
    if use_trailing_stop
        long_trailing_stop := long_stop

    bullish_confirm_count := 0
    partial_tp_taken := false

if short_signal
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=short_stop, limit=short_tp_full)

    // Initialize trailing stop
    if use_trailing_stop
        short_trailing_stop := short_stop

    bearish_confirm_count := 0
    partial_tp_taken := false

// Simplified trailing stop management - REMOVED to avoid conflicts
// Note: Trailing stops disabled to prevent unrealistic performance
// Can be re-enabled after proper testing of basic strategy

// Time-based exit (end of session)
if strategy.position_size != 0 and current_hour >= 15 and current_minute >= 45
    strategy.close_all(comment="Session End")

// ============================================================================
// ENHANCED VISUALIZATION
// ============================================================================

// Plot VWAP with dynamic coloring
vwap_color = price_above_vwap ? color.green : price_below_vwap ? color.red : color.blue
plot(vwap_value, color=vwap_color, linewidth=2, title="VWAP")

// Plot VWAP Standard Deviation Bands
plot(use_vwap_bands ? vwap_upper_band : na, color=color.blue, linewidth=1, style=plot.style_line, title="VWAP Upper Band")
plot(use_vwap_bands ? vwap_lower_band : na, color=color.blue, linewidth=1, style=plot.style_line, title="VWAP Lower Band")

// Plot trend EMAs
plot(trend_ema, color=strong_bullish_trend ? color.green : strong_bearish_trend ? color.red : color.gray, linewidth=2, title="Trend EMA")
plot(trend_ema_fast, color=color.orange, linewidth=1, title="Fast EMA")

// Background color for market conditions
trend_bg_color = strong_bullish_trend ? color.new(color.green, 95) :
                 strong_bearish_trend ? color.new(color.red, 95) :
                 trending_market ? color.new(color.yellow, 98) : na

bgcolor(trend_bg_color)

// High volatility warning
bgcolor(high_volatility ? color.new(color.purple, 90) : na)

// Entry signals with quality indication
plotshape(long_signal and high_quality_setup, style=shape.triangleup, location=location.belowbar,
          color=color.lime, size=size.normal, title="High Quality Long Signal")
plotshape(long_signal and not high_quality_setup, style=shape.triangleup, location=location.belowbar,
          color=color.green, size=size.small, title="Standard Long Signal")
plotshape(short_signal and high_quality_setup, style=shape.triangledown, location=location.abovebar,
          color=color.orange, size=size.normal, title="High Quality Short Signal")
plotshape(short_signal and not high_quality_setup, style=shape.triangledown, location=location.abovebar,
          color=color.red, size=size.small, title="Standard Short Signal")

// Volume spike indication
plotshape(volume_spike and entry_volume_spike, style=shape.circle, location=location.bottom,
          color=color.yellow, size=size.tiny, title="Volume Spike")

// Institutional activity markers
plotshape(institutional_activity, style=shape.diamond, location=location.top,
          color=color.purple, size=size.tiny, title="Smart Money Activity")

// Support and resistance levels
plot(swing_high, color=color.red, linewidth=1, style=plot.style_line, title="Swing High")
plot(swing_low, color=color.green, linewidth=1, style=plot.style_line, title="Swing Low")

// Debug plots (can be disabled later)
plotshape(bullish_pullback, style=shape.labelup, location=location.belowbar, color=color.blue, size=size.tiny, title="Bullish Pullback", text="BP")
plotshape(bearish_pullback, style=shape.labeldown, location=location.abovebar, color=color.blue, size=size.tiny, title="Bearish Pullback", text="BP")

// ============================================================================
// ENHANCED ALERTS
// ============================================================================

// High-quality setup alerts
alertcondition(long_signal and high_quality_setup, title="High Quality VWAP Long",
               message="High Quality VWAP Long Signal Detected")
alertcondition(short_signal and high_quality_setup, title="High Quality VWAP Short",
               message="High Quality VWAP Short Signal Detected")

// Standard alerts
alertcondition(long_signal, title="VWAP Long Signal", message="VWAP Reversion Long Entry Signal")
alertcondition(short_signal, title="VWAP Short Signal", message="VWAP Reversion Short Entry Signal")

// Market condition alerts
alertcondition(high_volatility and not high_volatility[1], title="High Volatility Warning",
               message="High volatility detected - Consider reducing position sizes")
alertcondition(institutional_activity, title="Smart Money Activity",
               message="Institutional activity detected - Large volume with strong price action")

// ============================================================================
// ENHANCED STRATEGY INFORMATION
// ============================================================================

// Display comprehensive strategy state
var table info_table = table.new(position.top_right, 2, 10, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Trend", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, strong_bullish_trend ? "Strong Bull" : strong_bearish_trend ? "Strong Bear" :
               bullish_trend ? "Bullish" : bearish_trend ? "Bearish" : "Neutral",
               text_color=color.white, bgcolor=strong_bullish_trend ? color.green : strong_bearish_trend ? color.red : color.gray)

    table.cell(info_table, 0, 1, "Market Regime", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, market_regime, text_color=color.black, bgcolor=color.white)

    table.cell(info_table, 0, 2, "VWAP Dist", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, str.tostring(vwap_distance, "#.##") + "%", text_color=color.black, bgcolor=color.white)

    table.cell(info_table, 0, 3, "Quality Score", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 3, str.tostring(entry_quality_score, "#.#") + "/6.5",
               text_color=color.white, bgcolor=high_quality_setup ? color.green : color.orange)

    table.cell(info_table, 0, 4, "Volatility", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 4, str.tostring(volatility_ratio, "#.##") + "x",
               text_color=color.white, bgcolor=high_volatility ? color.red : color.green)

    table.cell(info_table, 0, 5, "Volume", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 5, str.tostring(relative_volume, "#.##") + "x",
               text_color=color.white, bgcolor=volume_spike ? color.green : color.gray)

    table.cell(info_table, 0, 6, "Position", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 6, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "Flat",
               text_color=color.white, bgcolor=strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray)

    table.cell(info_table, 0, 7, "Position Size", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 7, str.tostring(dynamic_position_size, "#.#") + "%", text_color=color.black, bgcolor=color.white)

    table.cell(info_table, 0, 8, "RSI", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 8, str.tostring(rsi, "#"),
               text_color=color.white, bgcolor=rsi > 70 ? color.red : rsi < 30 ? color.green : color.gray)

    table.cell(info_table, 0, 9, "Session Time", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 9, time_allowed ? "Active" : "Closed",
               text_color=color.white, bgcolor=time_allowed ? color.green : color.red)
