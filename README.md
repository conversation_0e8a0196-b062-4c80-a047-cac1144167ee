# VWAP Reversion and Trend Confirmation Strategy

## Overview

This Pine Script implements a high-probability trading strategy based on Volume Weighted Average Price (VWAP) reversion within established trends. The strategy is specifically designed for NQ (NASDAQ futures) but can be applied to other liquid instruments.

## Strategy Logic

The VWAP acts as a dynamic support/resistance level that represents the true average price where institutions are active. This strategy capitalizes on pullbacks to VWAP within established trends, providing lower-risk entries with favorable risk/reward ratios.

### Core Components

1. **Trend Identification**: Uses EMA and higher timeframe analysis to determine the dominant trend
2. **VWAP Calculation**: Configurable VWAP with different reset periods (Session, Daily, Weekly, Monthly)
3. **Pullback Detection**: Identifies when price approaches VWAP within a defined threshold
4. **Confirmation Logic**: Requires multiple bars of confirmation before entry
5. **Risk Management**: ATR-based stop losses and configurable risk/reward ratios

## Key Features

### Input Parameters

#### VWAP Settings
- **VWAP Source**: Price source for VWAP calculation (default: HLC3)
- **VWAP Reset**: Reset period for VWAP (Session/Daily/Weekly/Monthly)

#### Trend Settings
- **Trend EMA Length**: Period for trend-following EMA (default: 20)
- **Higher Timeframe**: Higher timeframe for trend confirmation (default: 15min)

#### Entry Settings
- **Pullback Threshold**: Maximum distance from VWAP for entry consideration (default: 0.02%)
- **Confirmation Bars**: Number of bars required for entry confirmation (default: 2)

#### Risk Management
- **Stop Loss ATR Multiplier**: ATR-based stop loss distance (default: 2.0)
- **Take Profit R/R Ratio**: Risk/reward ratio for profit targets (default: 2.0)
- **ATR Length**: Period for ATR calculation (default: 14)

#### Time Filter
- **Use Time Filter**: Enable/disable trading time restrictions
- **Start/End Hours**: Trading session hours in EST

## Entry Conditions

### Bullish Setup
1. **Trend Confirmation**: Price above trend EMA, EMA rising, price above higher timeframe EMA
2. **VWAP Position**: Price consistently above VWAP (trend alignment)
3. **Pullback**: Price touches or slightly dips below VWAP
4. **Bounce Confirmation**: Price reclaims VWAP with momentum
5. **Time Filter**: Within allowed trading hours

### Bearish Setup
1. **Trend Confirmation**: Price below trend EMA, EMA falling, price below higher timeframe EMA
2. **VWAP Position**: Price consistently below VWAP (trend alignment)
3. **Pullback**: Price touches or slightly moves above VWAP
4. **Rejection Confirmation**: Price rejects VWAP and moves lower
5. **Time Filter**: Within allowed trading hours

## Risk Management

### Stop Loss
- Calculated using ATR (Average True Range) multiplier
- Placed below recent swing low (bullish) or above recent swing high (bearish)
- Default: 2.0 × ATR from entry price

### Take Profit
- Based on risk/reward ratio
- Default: 2:1 risk/reward ratio
- Automatically calculated based on stop loss distance

### Position Sizing
- Default: 10% of equity per trade
- Configurable through strategy settings

## Visual Elements

### Plots
- **VWAP Line**: Blue line showing current VWAP level
- **Trend EMA**: Color-coded line (green=bullish, red=bearish, gray=neutral)
- **VWAP Bands**: Dotted lines showing pullback threshold zones
- **Entry Signals**: Triangle shapes marking entry points

### Background
- Light green background during strong bullish trends
- Light red background during strong bearish trends

### Information Table
- Real-time display of:
  - Current trend direction
  - Distance from VWAP
  - Current position status

## Usage Instructions

1. **Apply to Chart**: Load the script on your NQ chart (or other liquid instrument)
2. **Configure Parameters**: Adjust input parameters based on your trading style and market conditions
3. **Monitor Signals**: Watch for triangle entry signals and background color changes
4. **Set Alerts**: Use built-in alert conditions for automated notifications

## Best Practices

### Market Conditions
- Works best in trending markets with clear directional bias
- Avoid during low-volume periods or major news events
- Most effective during regular trading hours (9 AM - 4 PM EST)

### Timeframes
- Primary timeframe: 5-15 minutes for NQ
- Higher timeframe confirmation: 15-60 minutes
- Avoid very short timeframes (< 5 minutes) due to noise

### Risk Management
- Never risk more than 1-2% of account per trade
- Use proper position sizing based on stop loss distance
- Consider market volatility when adjusting ATR multiplier

## Customization Options

### For Different Instruments
- Adjust ATR multiplier based on instrument volatility
- Modify pullback threshold for different price ranges
- Consider different VWAP reset periods for various trading styles

### For Different Trading Styles
- **Scalping**: Reduce confirmation bars, tighter stops
- **Swing Trading**: Increase confirmation bars, wider stops
- **Day Trading**: Use session VWAP reset, time filters

## Performance Considerations

### Strengths
- High probability setups aligned with institutional activity
- Clear risk/reward parameters
- Trend-following approach reduces counter-trend risks
- Multiple confirmation filters reduce false signals

### Limitations
- Requires trending markets for optimal performance
- May generate fewer signals in choppy/sideways markets
- Dependent on proper parameter optimization for specific instruments

## Alert Setup

The strategy includes built-in alert conditions:
- **VWAP Long Signal**: Triggered on bullish entry conditions
- **VWAP Short Signal**: Triggered on bearish entry conditions

Configure alerts in TradingView to receive notifications when conditions are met.

## Backtesting Notes

- Test on historical data with realistic spread/commission settings
- Consider slippage in volatile market conditions
- Validate performance across different market cycles
- Adjust parameters based on backtesting results

## Support and Updates

This implementation provides a solid foundation for VWAP reversion trading. Consider the following for optimization:
- Market-specific parameter tuning
- Additional confirmation indicators
- Enhanced risk management features
- Multi-timeframe analysis integration
