//@version=5
strategy("VWAP Reversion - Realistic", shorttitle="VWAP Realistic", overlay=true, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=10,
         commission_type=strategy.commission.percent, commission_value=0.05,
         slippage=2)

// ============================================================================
// INPUT PARAMETERS
// ============================================================================

// VWAP Settings
vwap_source = input.source(hlc3, title="VWAP Source", group="VWAP Settings")
use_vwap_bands = input.bool(true, title="Use VWAP Standard Deviation Bands", group="VWAP Settings")
vwap_std_mult = input.float(1.0, title="VWAP Std Dev Multiplier", minval=0.5, maxval=3.0, step=0.1, group="VWAP Settings")

// Trend Settings
trend_length = input.int(20, title="Trend EMA Length", minval=5, maxval=100, group="Trend Settings")
higher_tf = input.timeframe("15", title="Higher Timeframe for Trend", group="Trend Settings")

// Entry Settings
pullback_threshold = input.float(0.05, title="VWAP Pullback Threshold (%)", minval=0.01, maxval=1.0, step=0.01, group="Entry Settings")
confirmation_bars = input.int(2, title="Confirmation Bars", minval=1, maxval=5, group="Entry Settings")
use_volume_filter = input.bool(false, title="Use Volume Confirmation", group="Entry Settings")
volume_threshold = input.float(1.3, title="Volume Spike Threshold", minval=1.0, maxval=5.0, step=0.1, group="Entry Settings")

// Risk Management
stop_loss_atr_mult = input.float(2.5, title="Stop Loss ATR Multiplier", minval=1.0, maxval=5.0, step=0.1, group="Risk Management")
take_profit_rr = input.float(2.0, title="Take Profit Risk/Reward Ratio", minval=1.0, maxval=5.0, step=0.1, group="Risk Management")
atr_length = input.int(14, title="ATR Length", minval=5, maxval=50, group="Risk Management")

// Time Filter
use_time_filter = input.bool(true, title="Use Time Filter", group="Time Filter")
start_hour = input.int(9, title="Start Hour (EST)", minval=0, maxval=23, group="Time Filter")
end_hour = input.int(16, title="End Hour (EST)", minval=0, maxval=23, group="Time Filter")

// ============================================================================
// CALCULATIONS
// ============================================================================

// VWAP with Standard Deviation Bands
vwap_value = ta.vwap(vwap_source)
vwap_stdev = ta.stdev(vwap_source, 20)
vwap_upper_band = vwap_value + (vwap_stdev * vwap_std_mult)
vwap_lower_band = vwap_value - (vwap_stdev * vwap_std_mult)

// Trend Analysis
trend_ema = ta.ema(close, trend_length)
higher_tf_trend = request.security(syminfo.tickerid, higher_tf, ta.ema(close, trend_length))

// Volume Analysis
volume_ma = ta.sma(volume, 20)
volume_spike = volume > (volume_ma * volume_threshold)
relative_volume = volume / volume_ma

// Technical Indicators
rsi = ta.rsi(close, 14)
atr_value = ta.atr(atr_length)

// Time Filter
est_offset = -5
current_hour = hour(time, "UTC" + str.tostring(est_offset))
time_allowed = not use_time_filter or (current_hour >= start_hour and current_hour <= end_hour)

// ============================================================================
// TREND IDENTIFICATION
// ============================================================================

// Conservative trend conditions
bullish_trend = close > trend_ema and trend_ema > trend_ema[5] and close > higher_tf_trend
bearish_trend = close < trend_ema and trend_ema < trend_ema[5] and close < higher_tf_trend

// VWAP alignment
price_above_vwap = close > vwap_value
price_below_vwap = close < vwap_value

// Strong trend confirmation
strong_bullish_trend = bullish_trend and price_above_vwap
strong_bearish_trend = bearish_trend and price_below_vwap

// ============================================================================
// ENTRY CONDITIONS - REALISTIC
// ============================================================================

// VWAP pullback detection
vwap_distance = math.abs(close - vwap_value) / close * 100
near_vwap = vwap_distance <= pullback_threshold

// Realistic pullback zones
bullish_pullback_zone = use_vwap_bands ? (low <= vwap_value and low >= vwap_lower_band) : (low <= vwap_value)
bearish_pullback_zone = use_vwap_bands ? (high >= vwap_value and high <= vwap_upper_band) : (high >= vwap_value)

// REALISTIC setup conditions - NO LOOK-AHEAD BIAS
bullish_pullback = strong_bullish_trend[1] and bullish_pullback_zone and close > vwap_value
bullish_bounce_confirmation = close > open and close > vwap_value and (not use_volume_filter or volume_spike)

bearish_pullback = strong_bearish_trend[1] and bearish_pullback_zone and close < vwap_value
bearish_rejection_confirmation = close < open and close < vwap_value and (not use_volume_filter or volume_spike)

// Confirmation logic
var int bullish_confirm_count = 0
var int bearish_confirm_count = 0

if bullish_pullback
    bullish_confirm_count := 1
else if bullish_confirm_count > 0 and bullish_confirm_count < confirmation_bars and bullish_bounce_confirmation
    bullish_confirm_count += 1
else if not bullish_bounce_confirmation
    bullish_confirm_count := 0

if bearish_pullback
    bearish_confirm_count := 1
else if bearish_confirm_count > 0 and bearish_confirm_count < confirmation_bars and bearish_rejection_confirmation
    bearish_confirm_count += 1
else if not bearish_rejection_confirmation
    bearish_confirm_count := 0

// Final entry signals
long_signal = bullish_confirm_count >= confirmation_bars and time_allowed and strategy.position_size == 0
short_signal = bearish_confirm_count >= confirmation_bars and time_allowed and strategy.position_size == 0

// ============================================================================
// POSITION MANAGEMENT - REALISTIC
// ============================================================================

// Calculate stop loss and take profit levels
long_stop = close - (atr_value * stop_loss_atr_mult)
short_stop = close + (atr_value * stop_loss_atr_mult)

long_tp = close + (math.abs(close - long_stop) * take_profit_rr)
short_tp = close - (math.abs(short_stop - close) * take_profit_rr)

// REALISTIC entry orders - NO COMPLEX POSITION SIZING
if long_signal
    strategy.entry("Long", strategy.long)
    strategy.exit("Long Exit", "Long", stop=long_stop, limit=long_tp)
    bullish_confirm_count := 0

if short_signal
    strategy.entry("Short", strategy.short)
    strategy.exit("Short Exit", "Short", stop=short_stop, limit=short_tp)
    bearish_confirm_count := 0

// Time-based exit (realistic)
if strategy.position_size != 0 and current_hour >= 15 and minute(time) >= 45
    strategy.close_all(comment="Session End")

// ============================================================================
// VISUALIZATION
// ============================================================================

// Plot VWAP with dynamic coloring
vwap_color = price_above_vwap ? color.green : price_below_vwap ? color.red : color.blue
plot(vwap_value, color=vwap_color, linewidth=2, title="VWAP")

// Plot VWAP bands
plot(use_vwap_bands ? vwap_upper_band : na, color=color.blue, linewidth=1, style=plot.style_line, title="VWAP Upper Band")
plot(use_vwap_bands ? vwap_lower_band : na, color=color.blue, linewidth=1, style=plot.style_line, title="VWAP Lower Band")

// Plot trend EMA
plot(trend_ema, color=strong_bullish_trend ? color.green : strong_bearish_trend ? color.red : color.gray, linewidth=2, title="Trend EMA")

// Background for trend
bgcolor(strong_bullish_trend ? color.new(color.green, 95) : strong_bearish_trend ? color.new(color.red, 95) : na)

// Entry signals
plotshape(long_signal, style=shape.triangleup, location=location.belowbar, color=color.green, size=size.normal, title="Long Signal")
plotshape(short_signal, style=shape.triangledown, location=location.abovebar, color=color.red, size=size.normal, title="Short Signal")

// Volume spike indication
plotshape(volume_spike, style=shape.circle, location=location.bottom, color=color.yellow, size=size.tiny, title="Volume Spike")

// Debug plots
plotshape(bullish_pullback, style=shape.labelup, location=location.belowbar, color=color.blue, size=size.tiny, title="Bullish Pullback", text="BP")
plotshape(bearish_pullback, style=shape.labeldown, location=location.abovebar, color=color.blue, size=size.tiny, title="Bearish Pullback", text="BP")

// ============================================================================
// INFORMATION DISPLAY
// ============================================================================

var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Trend", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, strong_bullish_trend ? "Strong Bull" : strong_bearish_trend ? "Strong Bear" : "Neutral", 
               text_color=color.white, bgcolor=strong_bullish_trend ? color.green : strong_bearish_trend ? color.red : color.gray)
    
    table.cell(info_table, 0, 1, "VWAP Dist", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 1, str.tostring(vwap_distance, "#.##") + "%", text_color=color.black, bgcolor=color.white)
    
    table.cell(info_table, 0, 2, "Volume", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 2, str.tostring(relative_volume, "#.##") + "x", 
               text_color=color.white, bgcolor=volume_spike ? color.green : color.gray)
    
    table.cell(info_table, 0, 3, "RSI", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 3, str.tostring(rsi, "#"), 
               text_color=color.white, bgcolor=rsi > 70 ? color.red : rsi < 30 ? color.green : color.gray)
    
    table.cell(info_table, 0, 4, "ATR", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 4, str.tostring(atr_value, "#.##"), text_color=color.black, bgcolor=color.white)
    
    table.cell(info_table, 0, 5, "Position", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 5, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "Flat", 
               text_color=color.white, bgcolor=strategy.position_size > 0 ? color.green : strategy.position_size < 0 ? color.red : color.gray)

// ============================================================================
// ALERTS
// ============================================================================

alertcondition(long_signal, title="VWAP Long Signal", message="VWAP Reversion Long Entry Signal")
alertcondition(short_signal, title="VWAP Short Signal", message="VWAP Reversion Short Entry Signal")
alertcondition(volume_spike, title="Volume Spike", message="Volume spike detected")
